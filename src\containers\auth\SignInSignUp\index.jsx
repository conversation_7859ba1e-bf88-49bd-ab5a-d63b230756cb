'use client';
import { useState, useEffect, useRef } from 'react';
import { Link, Dialog, DialogContent, Box, Typography, Menu, MenuItem, DialogTitle } from '@mui/material';
import { SignInFlow, AccountSettings } from '@/components';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { resetIndividualUserToken } from '@/redux/actions/getIndividualUserOAuth';
import { resetIndividualUserInfo, saveIndividualUserInfo } from '@/redux/actions/getIndividualUserInfo';
import useNotification from '@/hooks/useNotification';
import { Loader } from '@/components/Loader';
import { Close } from '@mui/icons-material';
import { identifications, REGISTRATION_WIDGET } from '@/containers/commonConstants';
import { fetchWithCsrfToken } from '@/containers/commonUtility';
import { signIn, useSession, signOut } from 'next-auth/react';
import { GET_INDIVIDUAL_DATA } from '@/utils/constants/awsApiEndpoints';
import { getMachineAccessToken } from '@/network/authAPIUtility';
import { NETWORK, ORGANIZATION, MACHINE_ACCESS_TOKEN, ORGANIZATION_ID, INDIVIDUAL } from '@/utils/constants';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';
import { v4 as uuidv4 } from 'uuid';
import { resetGetIndividualData } from '@/redux/actions/getIndividualData';
import {
  API_HEADER_CONTENT_TYPE,
  API_HEADER_CONTENT_TYPE_JSON_VALUE,
  API_RESPONSE_201_SUCCESS,
  API_RESPONSE_204_SUCCESS,
  API_RESPONSE_AUTHENTICATION_409_ERROR,
  API_RESPONSE_SUCCESS,
  DEFAULT_ERROR_STATUS,
} from '../../../utils/constants/apiCodes';

export const SignInSignUp = (props) => {
  const {
    fetchIndividualData,
    checkConnectionAtConnectionIndex,
    fetchSearchIndividualAtClientIndex,
    makeConnectionAtConnectionIndex,
    fetchUpdateConnectionAtClientIndex,
  } = useCommonAPIs();
  const {
    isSignInDialogOpen,
    handleSignInDialogState,
    useWithoutDialogue = false,
    signInHeading,
    signUpHeading,
    buttonLabel,
    identification,
    widgetType,
  } = props;

  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { data: authSession, status: authStatus } = useSession();
  const [, sendNotification] = useNotification();

  const [anchorEl, setAnchorEl] = useState(null);
  const [isSignedIn, setIsSignedIn] = useState(false);
  const [signInTitle, setSignInTitle] = useState(signInHeading || 'Save for later');
  const [signUpTitle, setSignUpTitle] = useState(signUpHeading || 'Save for later');
  const [photoImageUrl, setPhotoImageUrl] = useState(null);
  const inactivityTimeoutRef = useRef();

  const {
    isIndividualUserInfoFetching,
    individualUserInfoSuccess,
    individualUserInfoSuccessData,
    individualUserInfoError,
    individualUserInfoErrorData,
  } = useSelector((state) => state.individualUserInfoReducer);
  const { getIndividualDataSuccessData } = useSelector((state) => state.getIndividualDataReducer);
  const {
    checkExistingConnectionIndexSuccess,
    checkExistingConnectionIndexSuccessData,
    checkExistingConnectionIndexError,
    isCheckExistingConnectionIndexFetching,
  } = useSelector((state) => state.checkExistingConnectionIndexReducer) || {};
  const { searchIndividualAtClientIndexSuccessData, searchIndividualAtClientIndexError } = useSelector(
    (state) => state.searchIndividualAtClientIndexReducer,
  );

  let { email = '', firstName = '', lastName = '', photoImage } = individualUserInfoSuccessData || {};

  const passwordValidationConstraints = {
    min: 6,
    max: 18,
    requireLowercase: true,
    requireUppercase: true,
    requireNumber: true,
    requireSpecial: true,
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSignOut = () => {
    signOut({ redirect: false });
    dispatch(resetIndividualUserToken());
    dispatch(resetIndividualUserInfo());
    dispatch(resetGetIndividualData());
    setPhotoImageUrl(null);
    setIsSignedIn(false);
    setAnchorEl(null);
  };

  const handleSignInDialogOpen = () => {
    setSignInTitle('Sign in');
    setSignUpTitle('Sign Up');
    handleSignInDialogState(true);
  };

  const handleSignInDialogClose = (isSignInCancelled) => {
    handleSignInDialogState(false, isSignInCancelled);
    setTimeout(() => {
      setSignInTitle(signInHeading || 'Save for later');
      setSignUpTitle(signUpHeading || 'Save for later');
    }, 200);
  };

  useEffect(() => {
    const fetchPhotoImage = async () => {
      if (!getIndividualDataSuccessData?.individualId) return;

      try {
        const headers = {
          [API_HEADER_CONTENT_TYPE]: API_HEADER_CONTENT_TYPE_JSON_VALUE,
          Authorization: `Bearer ${await getMachineAccessToken(INDIVIDUAL)}`,
        };

        const response = await fetch(`${GET_INDIVIDUAL_DATA}/${getIndividualDataSuccessData.individualId}/icon`, {
          headers,
        });

        const data = await response.json();
        data.iconUrl && setPhotoImageUrl(data.iconUrl);
      } catch (error) {
        console.error('Error fetching photo image:', error);
      }
    };

    fetchPhotoImage();
  }, [getIndividualDataSuccessData]);

  useEffect(() => {
    if (authStatus === 'authenticated' && authSession?.user) {
      dispatch(saveIndividualUserInfo(authSession?.user));
      handleSignInDialogClose(false);
      setIsSignedIn(true);
      // Fetch individual data and check connection index for consistent index updates
      fetchIndividualData(authSession?.user?.cambianId);
      checkConnectionAtConnectionIndex();
    }
  }, [authSession]);

  // Handle connection index check results for consistent index updates across all sign-in flows
  // Only proceed if Individual is signed in and has been identified
  useEffect(() => {
    if (getIndividualDataSuccessData && authSession?.user?.cambianId) {
      if (checkExistingConnectionIndexSuccess && checkExistingConnectionIndexSuccessData?.aliasId) {
        // Connection exists and we have a Client ID, update client index with Cambian ID
        const clientId = checkExistingConnectionIndexSuccessData.aliasId;
        const connectionData = {
          cambianId: authSession.user.cambianId,
          clientId: clientId,
        };
        fetchUpdateConnectionAtClientIndex(connectionData);
      } else if (checkExistingConnectionIndexError) {
        // Connection doesn't exist, search client index to identify the client first
        // This ensures we have a Client ID before attempting to create any connections
        const demographics = { individuals: [{}] };
        fetchSearchIndividualAtClientIndex(demographics, false);
      }
    }
  }, [
    getIndividualDataSuccessData,
    checkExistingConnectionIndexSuccess,
    checkExistingConnectionIndexSuccessData,
    checkExistingConnectionIndexError,
    authSession?.user?.cambianId,
  ]);

  // Add Connection (only if Signed In and have Client ID)
  // This process creates a connection for the Individual to the Organization
  // If the connection already exists this process does nothing
  useEffect(() => {
    if (searchIndividualAtClientIndexSuccessData && authSession?.user?.cambianId) {
      const { found, clientId } = searchIndividualAtClientIndexSuccessData?.data || {};

      // Only create connection if:
      // 1. Individual is signed in (authSession?.user?.cambianId exists)
      // 2. Client has been identified in Client Index (found && clientId exists)
      // 3. Connection doesn't already exist (checkExistingConnectionIndexError indicates no existing connection)
      if (found && clientId && checkExistingConnectionIndexError) {
        // Store Organization ID and Client ID in Connection Index for this Cambian ID
        makeConnectionAtConnectionIndex(clientId);
      }
    }
  }, [searchIndividualAtClientIndexSuccessData, checkExistingConnectionIndexError, authSession?.user?.cambianId]);

  /**
   *
   * @param {string} username
   * @param {string} password
   * @param {string} firstName
   * @param {string} lastName
   *
   * @returns {Promise<{ success: boolean, isVerificationDisabled: boolean, errorMsg: string }>}
   * - `success` (boolean): Indicates whether the sign-up was successful.
   * - `isVerificationDisabled` (boolean): Indicates if email verification is disabled for the user.
   * - `errorMsg` (string): An error message in case of failure.
   */
  async function signUpCallback({ username, password, firstName, lastName }) {
    try {
      const URL = GET_INDIVIDUAL_DATA;
      const commonHeaders = {};
      commonHeaders[API_HEADER_CONTENT_TYPE] = API_HEADER_CONTENT_TYPE_JSON_VALUE;

      const headers = {
        ...commonHeaders,
        Authorization: `Bearer ${await getMachineAccessToken(INDIVIDUAL)}`,
      };
      const data = {
        firstName,
        lastName,
        emailAddresses: [
          {
            id: uuidv4(),
            emailAddress: username,
            primary: true,
            note: 'email',
          },
        ],
      };
      const params = {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
      };

      const individualResult = await fetch(URL, params);
      const individualResultData = await individualResult.json();

      if (individualResult.status === 200) {
        try {
          //call signup api
          const URL = `${process.env.NODE_ENV !== 'production' ? 'http' : 'https'}://${
            window.location.host
          }/api/auth/signup`; // TODO: Move this to api constant files
          const result = await fetchWithCsrfToken(URL, {
            email: username,
            cambianId: individualResultData.individualId,
            password,
            firstName,
            lastName,
          });

          if (result.status === 200) {
            fetchIndividualData(individualResultData.individualId);
          }
          return { success: result.status === 200 };
        } catch (err) {
          return { success: false, errorMsg: err?.response?.data?.message };
        }
      } else if (individualResult.status === 403) {
        return { success: false, errorMsg: individualResultData.message };
      }
    } catch (error) {
      if (error.errorName === 'UsernameExistsException') {
        return { success: false, errorMsg: 'Username already exists' };
      }
      return { success: false, errorMsg: error?.response?.data?.message };
    }
  }

  const signInCallback = async ({ username, password }) => {
    try {
      const result = await signIn('cognito', {
        email: username,
        password,
        redirect: false,
      });
      if (result.ok) {
        // * Will be removed in coming time, keeping it for testing purpose
        return { success: true, response: result, errorMsg: null };
      }
      const error = JSON.parse(result.error);
      return { success: false, errorName: error.name, errorMsg: error.message };
    } catch (err) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
      return { success: false };
    }
  };

  const changePasswordCallback = async ({ username, verificationCode, newPassword }) => {
    try {
      const result = await fetchWithCsrfToken(
        `${process.env.NODE_ENV !== 'production' ? 'http' : 'https'}://${
          window.location.host
        }/api/auth/confirmForgotPassword`, // TODO: Move this to api constant files
        {
          email: username,
          verificationCode,
          newPassword,
        },
      );

      return { success: result.status === 200 };
    } catch (error) {
      return { success: false, errorMsg: error?.response?.data?.message };
    }
  };

  const forgotPasswordCallback = async ({ username }) => {
    try {
      const result = await fetchWithCsrfToken(
        `${process.env.NODE_ENV !== 'production' ? 'http' : 'https'}://${window.location.host}/api/auth/forgotPassword`, // TODO: Move this to api constant files
        {
          email: username,
        },
      );

      return { success: result.status === 200 };
    } catch (error) {
      return { success: false, errorMsg: error?.response?.data?.message };
    }
  };

  const verifyRegistrationAndSigninCallback = async ({ verificationCode }) => {
    try {
      if (verificationCode !== null) {
        verificationCode = verificationCode.trim();
      }
      const result = await fetchWithCsrfToken(
        `${process.env.NODE_ENV !== 'production' ? 'http' : 'https'}://${window.location.host}/api/auth/confirmSignup`, // TODO: Move this to api constant files
        {
          verificationCode,
        },
      );

      return { success: result.status === 200 };
    } catch (error) {
      return { success: false, errorMsg: error?.response?.data?.message };
    }
  };

  const resendVerificationCodeCallbackForSignup = async () => {
    try {
      const result = await fetchWithCsrfToken(
        `${process.env.NODE_ENV !== 'production' ? 'http' : 'https'}://${
          window.location.host
        }/api/auth/resendVerificationCode`, // TODO: Move this to api constant files
      );

      return { success: result.status === 200 };
    } catch (error) {
      return { success: false, errorMsg: error?.response?.data?.message };
    }
  };

  const getSignInComponent = () => {
    return (
      <SignInFlow
        signInProps={{
          signInCallback,
          title: signInTitle,
          buttonText: buttonLabel,
        }}
        signUpProps={{
          signUpCallback,
          title: signUpTitle,
          buttonText: buttonLabel,
          passwordRestriction: passwordValidationConstraints,
        }}
        forgotPasswordProps={{ forgotPasswordCallback }}
        verifyAndChangePasswordProps={{
          changePasswordCallback,
          resendVerificationCodeCallback: forgotPasswordCallback,
        }}
        verifyRegistrationProps={{
          verifyRegistrationCallback: verifyRegistrationAndSigninCallback,
          resendVerificationCodeCallback: resendVerificationCodeCallbackForSignup,
          autoSignInEnabled: true,
        }}
        sharedProps={{
          logoUrl: process.env.NEXT_PUBLIC_SIGN_IN_PAGES_LOGO || undefined,
        }}
      />
    );
  };

  return (
    <>
      <Loader active={isIndividualUserInfoFetching} />
      {useWithoutDialogue ? (
        <>{getSignInComponent()}</>
      ) : (
        <>
          {!isSignInDialogOpen && (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'flex-end',
                pr: { xs: 2, md: '4.5%' },
                mt: 0.5,
              }}
            >
              {!isSignedIn ? (
                <Typography>
                  <Link onClick={handleSignInDialogOpen} sx={{ cursor: 'pointer', pr: 1 }}>
                    {t('signIn')}
                  </Link>
                </Typography>
              ) : (
                <Box
                  onClick={handleMenuOpen}
                  sx={{
                    cursor: 'pointer',
                  }}
                >
                  <AccountSettings
                    firstName={firstName}
                    lastName={lastName}
                    photoImageUrl={photoImageUrl}
                    photoImage={photoImage}
                    email={email}
                  />
                </Box>
              )}
              <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
                MenuListProps={{
                  'aria-labelledby': 'basic-button',
                }}
                sx={{ mt: 0.5 }}
              >
                <MenuItem sx={{ minWidth: '12vw' }} onClick={handleSignOut}>
                  Sign out
                </MenuItem>
              </Menu>
            </Box>
          )}

          <Dialog open={isSignInDialogOpen}>
            <DialogTitle sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
              {!(widgetType === REGISTRATION_WIDGET && identification === identifications.DEIDENTIFIED) && (
                <Close sx={{ cursor: 'pointer', fontSize: '20px' }} onClick={() => handleSignInDialogClose(true)} />
              )}
            </DialogTitle>
            <DialogContent sx={{ overflow: 'hidden' }}>{getSignInComponent()}</DialogContent>
          </Dialog>
        </>
      )}
    </>
  );
};
